Universal Extractor - 使用说明
================================

文件说明:
- extractor_gui.exe: 图形界面版本（双击运行）
- universal_extractor.exe: 命令行版本
- 启动图形界面.bat: 启动图形界面的批处理文件
- extract.bat: 快速解压批处理（拖拽文件到此文件上）

使用方法:
1. 图形界面: 双击 "启动图形界面.bat" 或 "extractor_gui.exe"
2. 命令行: 在命令提示符中运行 "universal_extractor.exe 文件名"
3. 快速解压: 将压缩文件拖拽到 "extract.bat" 上

支持格式:
ZIP, RAR, 7Z, TAR, TAR.GZ, TAR.BZ2, TAR.XZ, GZ, BZ2, XZ

注意事项:
- RAR 和 7Z 格式可能需要额外的工具支持
- 确保有足够的磁盘空间进行解压
- 某些格式需要安装额外的 Python 库

更多信息请查看 README.md 文件。
