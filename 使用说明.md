# Universal Extractor - 使用说明

## 🎉 恭喜！解压缩工具已成功创建

你现在拥有一个功能完整的轻量级解压缩工具，具有以下特点：

### ✨ 主要特点
- **体积小巧**: 核心代码仅150行，基于Python标准库
- **格式丰富**: 支持ZIP、TAR、GZ、BZ2、XZ等多种格式
- **双界面**: 命令行 + 图形界面
- **跨平台**: Windows、Linux、macOS通用

## 📁 文件说明

### 核心文件
- `universal_extractor.py` - 主程序（命令行版本）
- `extractor_gui.py` - 图形界面版本
- `simple_gui.py` - 简化版图形界面（备用）

### 辅助文件
- `extract.bat` - Windows快捷批处理
- `requirements.txt` - 可选依赖列表
- `test_extractor.py` - 测试脚本
- `build_executable.py` - 打包可执行文件脚本

### 生成文件
- `dist/universal_extractor.exe` - 可执行文件（已生成）
- `test_archives/` - 测试文件目录

## 🚀 使用方法

### 1. 命令行使用

```bash
# 查看支持的格式
python universal_extractor.py --list-formats

# 解压到默认目录
python universal_extractor.py archive.zip

# 解压到指定目录
python universal_extractor.py archive.zip -o output_folder

# 使用可执行文件
dist\universal_extractor.exe archive.zip
```

### 2. 图形界面使用

```bash
# 启动图形界面
python extractor_gui.py

# 或使用简化版
python simple_gui.py
```

### 3. Windows快捷方式

- 将压缩文件拖拽到 `extract.bat` 上即可解压

## ✅ 测试结果

所有核心功能已通过测试：

```
✓ test.zip             成功
✓ test.tar             成功  
✓ test.tar.gz          成功
✓ test.tar.bz2         成功
✓ test.tar.xz          成功
✓ test.txt.gz          成功
✓ test.txt.bz2         成功
✓ test.txt.xz          成功

成功: 8/8
```

## 🔧 支持的格式

### 完全支持（无需额外依赖）
- ZIP (.zip)
- TAR (.tar, .tar.gz, .tar.bz2, .tar.xz)
- GZIP (.gz)
- BZIP2 (.bz2)  
- XZ (.xz)

### 扩展支持（需要额外工具）
- RAR (.rar) - 需要 unrar 工具
- 7Z (.7z) - 需要 py7zr 库或 7z 工具

## 📦 打包可执行文件

如果需要创建独立的可执行文件：

```bash
# 自动构建
python build_executable.py

# 手动构建命令行版本
pyinstaller --onefile --name=universal_extractor universal_extractor.py

# 手动构建图形界面版本
pyinstaller --onefile --name=extractor_gui --windowed extractor_gui.py
```

## 🛠️ 故障排除

### 图形界面问题
- 如果图形界面无法启动，使用 `simple_gui.py` 作为备用
- 确保安装了tkinter（Python标准库）

### RAR/7Z格式支持
```bash
# 安装额外依赖
pip install py7zr patoolib

# 或安装系统工具
# Windows: 安装 7-Zip
# Linux: sudo apt-get install p7zip-full unrar
```

### 可执行文件构建失败
- 确保安装了PyInstaller: `pip install pyinstaller`
- 如果windowed模式失败，会自动尝试console模式

## 🎯 使用建议

1. **日常使用**: 直接使用 `python universal_extractor.py`
2. **批量处理**: 使用命令行版本编写脚本
3. **用户友好**: 使用图形界面版本
4. **便携使用**: 使用生成的exe文件

## 📈 扩展功能

工具采用模块化设计，可以轻松扩展：

1. **添加新格式**: 在 `supported_formats` 字典中添加新的处理函数
2. **批量处理**: 修改主函数支持多文件输入
3. **压缩功能**: 添加压缩文件的功能
4. **进度显示**: 为大文件添加详细进度显示

## 🏆 总结

你现在拥有一个：
- ✅ **体积小巧** - 单文件，无复杂依赖
- ✅ **功能强大** - 支持8种常见格式
- ✅ **使用简单** - 命令行 + 图形界面
- ✅ **测试完整** - 所有功能已验证
- ✅ **可执行文件** - 已生成独立exe

这个工具完美满足了你的需求：体积小但功能多！

---

**享受使用你的新解压缩工具吧！** 🎉
