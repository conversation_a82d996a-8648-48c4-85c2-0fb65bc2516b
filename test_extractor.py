#!/usr/bin/env python3
"""
测试脚本 - 验证解压缩工具的功能
"""

import os
import tempfile
import zipfile
import tarfile
import gzip
import bz2
import lzma
from pathlib import Path
from universal_extractor import UniversalExtractor

def create_test_files():
    """创建测试用的压缩文件"""
    test_dir = Path("test_archives")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试内容
    test_content = "这是一个测试文件的内容\nTest file content\n测试中文内容"
    
    # 创建ZIP文件
    with zipfile.ZipFile(test_dir / "test.zip", 'w') as zf:
        zf.writestr("test.txt", test_content)
        zf.writestr("folder/nested.txt", "嵌套文件内容")
    
    # 创建TAR文件
    with tarfile.open(test_dir / "test.tar", 'w') as tf:
        info = tarfile.TarInfo("test.txt")
        info.size = len(test_content.encode())
        tf.addfile(info, fileobj=tarfile.io.BytesIO(test_content.encode()))
    
    # 创建TAR.GZ文件
    with tarfile.open(test_dir / "test.tar.gz", 'w:gz') as tf:
        info = tarfile.TarInfo("test.txt")
        info.size = len(test_content.encode())
        tf.addfile(info, fileobj=tarfile.io.BytesIO(test_content.encode()))
    
    # 创建TAR.BZ2文件
    with tarfile.open(test_dir / "test.tar.bz2", 'w:bz2') as tf:
        info = tarfile.TarInfo("test.txt")
        info.size = len(test_content.encode())
        tf.addfile(info, fileobj=tarfile.io.BytesIO(test_content.encode()))
    
    # 创建TAR.XZ文件
    with tarfile.open(test_dir / "test.tar.xz", 'w:xz') as tf:
        info = tarfile.TarInfo("test.txt")
        info.size = len(test_content.encode())
        tf.addfile(info, fileobj=tarfile.io.BytesIO(test_content.encode()))
    
    # 创建GZ文件
    with gzip.open(test_dir / "test.txt.gz", 'wt', encoding='utf-8') as f:
        f.write(test_content)
    
    # 创建BZ2文件
    with bz2.open(test_dir / "test.txt.bz2", 'wt', encoding='utf-8') as f:
        f.write(test_content)
    
    # 创建XZ文件
    with lzma.open(test_dir / "test.txt.xz", 'wt', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"测试文件已创建在 {test_dir} 目录中")
    return test_dir

def test_extraction():
    """测试解压功能"""
    print("开始测试解压功能...")
    
    # 创建测试文件
    test_dir = create_test_files()
    
    # 创建解压器
    extractor = UniversalExtractor()
    
    # 测试每种格式
    test_files = [
        "test.zip",
        "test.tar",
        "test.tar.gz",
        "test.tar.bz2", 
        "test.tar.xz",
        "test.txt.gz",
        "test.txt.bz2",
        "test.txt.xz"
    ]
    
    results = {}
    
    for test_file in test_files:
        archive_path = test_dir / test_file
        if archive_path.exists():
            output_dir = test_dir / f"extracted_{test_file.replace('.', '_')}"
            
            print(f"\n测试解压: {test_file}")
            try:
                success = extractor.extract(str(archive_path), str(output_dir))
                results[test_file] = "成功" if success else "失败"
                
                # 验证解压结果
                if success and output_dir.exists():
                    files = list(output_dir.rglob("*"))
                    print(f"  解压出 {len(files)} 个文件/目录")
                    for file in files[:3]:  # 只显示前3个
                        print(f"    {file.relative_to(output_dir)}")
                    if len(files) > 3:
                        print(f"    ... 还有 {len(files) - 3} 个文件")
                
            except Exception as e:
                results[test_file] = f"错误: {e}"
                print(f"  错误: {e}")
    
    # 显示测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    for test_file, result in results.items():
        status = "✓" if result == "成功" else "✗"
        print(f"{status} {test_file:<20} {result}")
    
    # 统计
    success_count = sum(1 for r in results.values() if r == "成功")
    total_count = len(results)
    print(f"\n成功: {success_count}/{total_count}")
    
    return results

def test_format_detection():
    """测试格式检测"""
    print("\n测试格式检测...")
    
    extractor = UniversalExtractor()
    
    test_cases = [
        ("test.zip", ".zip"),
        ("archive.tar.gz", ".tar.gz"),
        ("data.tar.bz2", ".tar.bz2"),
        ("file.tar.xz", ".tar.xz"),
        ("document.7z", ".7z"),
        ("backup.rar", ".rar"),
        ("compressed.gz", ".gz"),
        ("data.bz2", ".bz2"),
        ("file.xz", ".xz"),
    ]
    
    for filename, expected in test_cases:
        detected = extractor.get_file_extension(filename)
        status = "✓" if detected == expected else "✗"
        print(f"{status} {filename:<20} 检测为: {detected} (期望: {expected})")

def main():
    """主测试函数"""
    print("Universal Extractor 测试程序")
    print("="*50)
    
    # 测试格式检测
    test_format_detection()
    
    # 测试解压功能
    test_extraction()
    
    print("\n测试完成!")

if __name__ == '__main__':
    main()
