@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo Universal Extractor - 解压缩工具
echo ================================

if "%~1"=="" (
    echo 用法: extract.bat [压缩文件路径] [输出目录]
    echo 或者: 直接拖拽压缩文件到此批处理文件上
    echo.
    echo 示例:
    echo   extract.bat archive.zip
    echo   extract.bat archive.zip C:\output
    echo.
    pause
    exit /b 1
)

set "archive=%~1"
set "output=%~2"

if not exist "%archive%" (
    echo 错误: 文件不存在 "%archive%"
    pause
    exit /b 1
)

echo 正在解压: %archive%

if "%output%"=="" (
    python "%~dp0universal_extractor.py" "%archive%"
) else (
    python "%~dp0universal_extractor.py" "%archive%" -o "%output%"
)

if %errorlevel% equ 0 (
    echo.
    echo 解压完成!
) else (
    echo.
    echo 解压失败!
)

echo.
pause
