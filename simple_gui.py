#!/usr/bin/env python3
"""
简化版图形界面 - 用于测试和确保兼容性
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from pathlib import Path
from universal_extractor import UniversalExtractor

class SimpleExtractorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Universal Extractor - 简化版")
        self.root.geometry("500x300")
        
        self.extractor = UniversalExtractor()
        self.setup_ui()
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择
        ttk.Label(main_frame, text="选择压缩文件:").pack(anchor=tk.W, pady=(0, 5))
        
        file_frame = ttk.Frame(main_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var)
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT)
        
        # 输出目录
        ttk.Label(main_frame, text="输出目录:").pack(anchor=tk.W, pady=(0, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        ttk.Button(output_frame, text="浏览", command=self.browse_output).pack(side=tk.RIGHT)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        self.extract_btn = ttk.Button(button_frame, text="开始解压", command=self.start_extraction)
        self.extract_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="支持格式", command=self.show_formats).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=10)
        
        # 状态标签
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.pack(anchor=tk.W)
    
    def browse_file(self):
        filetypes = [
            ("所有支持的格式", "*.zip;*.rar;*.7z;*.tar;*.tar.gz;*.tgz;*.tar.bz2;*.tbz2;*.tar.xz;*.txz;*.gz;*.bz2;*.xz"),
            ("ZIP文件", "*.zip"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择压缩文件",
            filetypes=filetypes
        )
        
        if filename:
            self.file_var.set(filename)
            # 自动设置输出目录
            if not self.output_var.get():
                output_dir = Path(filename).parent / Path(filename).stem
                self.output_var.set(str(output_dir))
    
    def browse_output(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_var.set(directory)
    
    def start_extraction(self):
        if not self.file_var.get():
            messagebox.showerror("错误", "请选择要解压的文件")
            return
        
        # 在新线程中执行解压
        thread = threading.Thread(target=self.extract_file)
        thread.daemon = True
        thread.start()
    
    def extract_file(self):
        try:
            self.extract_btn.config(state='disabled')
            self.progress.start()
            self.status_var.set("正在解压...")
            
            archive_path = self.file_var.get()
            output_dir = self.output_var.get() or None
            
            success = self.extractor.extract(archive_path, output_dir)
            
            if success:
                self.status_var.set("解压完成")
                messagebox.showinfo("成功", "文件解压完成!")
            else:
                self.status_var.set("解压失败")
                messagebox.showerror("错误", "文件解压失败!")
                
        except Exception as e:
            self.status_var.set("发生错误")
            messagebox.showerror("错误", f"解压过程中出现错误:\n{e}")
        finally:
            self.progress.stop()
            self.extract_btn.config(state='normal')
            if self.status_var.get() == "正在解压...":
                self.status_var.set("就绪")
    
    def show_formats(self):
        formats = self.extractor.list_supported_formats()
        format_text = "支持的格式:\n\n" + "\n".join(f"• {fmt}" for fmt in formats)
        messagebox.showinfo("支持的格式", format_text)

def main():
    root = tk.Tk()
    app = SimpleExtractorGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
