#!/usr/bin/env python3
"""
Universal Extractor GUI - 图形界面版本
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from pathlib import Path
from universal_extractor import UniversalExtractor

class ExtractorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Universal Extractor - 解压缩工具")
        self.root.geometry("600x400")
        
        self.extractor = UniversalExtractor()
        self.setup_ui()
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择
        ttk.Label(main_frame, text="选择压缩文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var, width=50)
        self.file_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=1)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=(15, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(output_frame, text="浏览", command=self.browse_output).grid(row=0, column=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        self.extract_btn = ttk.Button(button_frame, text="开始解压", command=self.start_extraction)
        self.extract_btn.grid(row=0, column=0, padx=5)
        
        ttk.Button(button_frame, text="支持格式", command=self.show_formats).grid(row=0, column=1, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # 日志区域
        ttk.Label(main_frame, text="日志:").grid(row=6, column=0, sticky=tk.W, pady=(10, 5))
        
        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        self.log_text = tk.Text(log_frame, height=10, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        file_frame.columnconfigure(0, weight=1)
        output_frame.columnconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 拖拽支持
        self.root.drop_target_register(tk.DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_drop)
    
    def browse_file(self):
        filetypes = [
            ("所有支持的格式", "*.zip;*.rar;*.7z;*.tar;*.tar.gz;*.tgz;*.tar.bz2;*.tbz2;*.tar.xz;*.txz;*.gz;*.bz2;*.xz"),
            ("ZIP文件", "*.zip"),
            ("RAR文件", "*.rar"),
            ("7Z文件", "*.7z"),
            ("TAR文件", "*.tar;*.tar.gz;*.tgz;*.tar.bz2;*.tbz2;*.tar.xz;*.txz"),
            ("压缩文件", "*.gz;*.bz2;*.xz"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择压缩文件",
            filetypes=filetypes
        )
        
        if filename:
            self.file_var.set(filename)
            # 自动设置输出目录
            if not self.output_var.get():
                output_dir = Path(filename).parent / Path(filename).stem
                self.output_var.set(str(output_dir))
    
    def browse_output(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_var.set(directory)
    
    def on_drop(self, event):
        files = self.root.tk.splitlist(event.data)
        if files:
            self.file_var.set(files[0])
            if not self.output_var.get():
                output_dir = Path(files[0]).parent / Path(files[0]).stem
                self.output_var.set(str(output_dir))
    
    def log(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_extraction(self):
        if not self.file_var.get():
            messagebox.showerror("错误", "请选择要解压的文件")
            return
        
        # 在新线程中执行解压
        thread = threading.Thread(target=self.extract_file)
        thread.daemon = True
        thread.start()
    
    def extract_file(self):
        try:
            self.extract_btn.config(state='disabled')
            self.progress.start()
            
            archive_path = self.file_var.get()
            output_dir = self.output_var.get() or None
            
            self.log(f"开始解压: {Path(archive_path).name}")
            
            success = self.extractor.extract(archive_path, output_dir)
            
            if success:
                self.log("解压完成!")
                messagebox.showinfo("成功", "文件解压完成!")
            else:
                self.log("解压失败!")
                messagebox.showerror("错误", "文件解压失败!")
                
        except Exception as e:
            self.log(f"错误: {e}")
            messagebox.showerror("错误", f"解压过程中出现错误:\n{e}")
        finally:
            self.progress.stop()
            self.extract_btn.config(state='normal')
    
    def show_formats(self):
        formats = self.extractor.list_supported_formats()
        format_text = "支持的格式:\n\n" + "\n".join(f"• {fmt}" for fmt in formats)
        messagebox.showinfo("支持的格式", format_text)

def main():
    root = tk.Tk()
    app = ExtractorGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
