#!/usr/bin/env python3
"""
构建可执行文件的脚本
使用 PyInstaller 将 Python 脚本打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查是否安装了 PyInstaller"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """安装 PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        print("PyInstaller 安装成功!")
        return True
    except subprocess.CalledProcessError:
        print("PyInstaller 安装失败!")
        return False

def build_cli_executable():
    """构建命令行版本的可执行文件"""
    print("构建命令行版本...")
    
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--name=universal_extractor',   # 可执行文件名
        '--console',                    # 控制台应用
        '--clean',                      # 清理临时文件
        'universal_extractor.py'
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("命令行版本构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令行版本构建失败: {e}")
        return False

def build_gui_executable():
    """构建图形界面版本的可执行文件"""
    print("构建图形界面版本...")

    # 首先尝试windowed模式
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--name=extractor_gui',         # 可执行文件名
        '--windowed',                   # 无控制台窗口
        '--clean',                      # 清理临时文件
        'extractor_gui.py'
    ]

    try:
        subprocess.run(cmd, check=True)
        print("图形界面版本构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"图形界面windowed模式构建失败: {e}")
        print("尝试console模式...")

        # 如果windowed模式失败，尝试console模式
        cmd_console = [
            'pyinstaller',
            '--onefile',
            '--name=extractor_gui_console',
            '--console',
            '--clean',
            'extractor_gui.py'
        ]

        try:
            subprocess.run(cmd_console, check=True)
            print("图形界面console模式构建成功!")
            return True
        except subprocess.CalledProcessError as e2:
            print(f"图形界面console模式也构建失败: {e2}")
            return False

def create_portable_package():
    """创建便携版包"""
    print("创建便携版包...")
    
    # 创建便携版目录
    portable_dir = Path("Universal_Extractor_Portable")
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    
    portable_dir.mkdir()
    
    # 复制文件
    files_to_copy = [
        "universal_extractor.py",
        "extractor_gui.py", 
        "requirements.txt",
        "README.md",
        "extract.bat"
    ]
    
    for file in files_to_copy:
        if Path(file).exists():
            shutil.copy2(file, portable_dir)
    
    # 复制可执行文件（如果存在）
    dist_dir = Path("dist")
    if dist_dir.exists():
        for exe_file in dist_dir.glob("*.exe"):
            shutil.copy2(exe_file, portable_dir)
    
    # 创建启动脚本
    start_gui_bat = portable_dir / "启动图形界面.bat"
    with open(start_gui_bat, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
echo 启动 Universal Extractor 图形界面...

if exist "extractor_gui.exe" (
    start "" "extractor_gui.exe"
) else (
    if exist "python.exe" (
        python extractor_gui.py
    ) else (
        python extractor_gui.py
    )
)
""")
    
    # 创建使用说明
    usage_txt = portable_dir / "使用说明.txt"
    with open(usage_txt, 'w', encoding='utf-8') as f:
        f.write("""Universal Extractor - 使用说明
================================

文件说明:
- extractor_gui.exe: 图形界面版本（双击运行）
- universal_extractor.exe: 命令行版本
- 启动图形界面.bat: 启动图形界面的批处理文件
- extract.bat: 快速解压批处理（拖拽文件到此文件上）

使用方法:
1. 图形界面: 双击 "启动图形界面.bat" 或 "extractor_gui.exe"
2. 命令行: 在命令提示符中运行 "universal_extractor.exe 文件名"
3. 快速解压: 将压缩文件拖拽到 "extract.bat" 上

支持格式:
ZIP, RAR, 7Z, TAR, TAR.GZ, TAR.BZ2, TAR.XZ, GZ, BZ2, XZ

注意事项:
- RAR 和 7Z 格式可能需要额外的工具支持
- 确保有足够的磁盘空间进行解压
- 某些格式需要安装额外的 Python 库

更多信息请查看 README.md 文件。
""")
    
    print(f"便携版包已创建: {portable_dir}")
    return True

def main():
    """主函数"""
    print("Universal Extractor 构建工具")
    print("="*40)
    
    # 检查 PyInstaller
    if not check_pyinstaller():
        print("未找到 PyInstaller，正在安装...")
        if not install_pyinstaller():
            print("无法安装 PyInstaller，请手动安装: pip install pyinstaller")
            return False
    
    print("开始构建可执行文件...")
    
    # 构建命令行版本
    cli_success = build_cli_executable()
    
    # 构建图形界面版本
    gui_success = build_gui_executable()
    
    # 创建便携版包
    portable_success = create_portable_package()
    
    # 清理临时文件
    temp_dirs = ['build', '__pycache__']
    for temp_dir in temp_dirs:
        if Path(temp_dir).exists():
            shutil.rmtree(temp_dir)
    
    # 清理 .spec 文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
    
    print("\n" + "="*40)
    print("构建结果:")
    print(f"命令行版本: {'成功' if cli_success else '失败'}")
    print(f"图形界面版本: {'成功' if gui_success else '失败'}")
    print(f"便携版包: {'成功' if portable_success else '失败'}")
    
    if cli_success or gui_success:
        print(f"\n可执行文件位于 dist/ 目录中")
        print(f"便携版包位于 Universal_Extractor_Portable/ 目录中")
    
    return cli_success or gui_success

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
