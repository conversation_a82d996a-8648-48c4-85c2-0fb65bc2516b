#!/usr/bin/env python3
"""
Universal Extractor - 轻量级多格式解压缩工具
支持格式: ZIP, RAR, 7Z, TAR, GZ, BZ2, XZ, LZ4, ZSTD等
"""

import os
import sys
import argparse
import zipfile
import tarfile
import gzip
import bz2
import lzma
import shutil
from pathlib import Path
import tempfile
import subprocess

class UniversalExtractor:
    def __init__(self):
        self.supported_formats = {
            '.zip': self._extract_zip,
            '.rar': self._extract_rar,
            '.7z': self._extract_7z,
            '.tar': self._extract_tar,
            '.tar.gz': self._extract_tar_gz,
            '.tgz': self._extract_tar_gz,
            '.tar.bz2': self._extract_tar_bz2,
            '.tbz2': self._extract_tar_bz2,
            '.tar.xz': self._extract_tar_xz,
            '.txz': self._extract_tar_xz,
            '.gz': self._extract_gz,
            '.bz2': self._extract_bz2,
            '.xz': self._extract_xz,
        }
    
    def get_file_extension(self, filename):
        """获取文件扩展名，支持复合扩展名"""
        filename = filename.lower()
        for ext in ['.tar.gz', '.tar.bz2', '.tar.xz']:
            if filename.endswith(ext):
                return ext
        return Path(filename).suffix
    
    def extract(self, archive_path, output_dir=None):
        """解压文件到指定目录"""
        archive_path = Path(archive_path)
        if not archive_path.exists():
            raise FileNotFoundError(f"文件不存在: {archive_path}")
        
        # 确定输出目录
        if output_dir is None:
            output_dir = archive_path.parent / archive_path.stem
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取文件扩展名
        ext = self.get_file_extension(archive_path.name)
        
        if ext not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {ext}")
        
        print(f"正在解压 {archive_path.name} 到 {output_dir}")
        
        try:
            self.supported_formats[ext](archive_path, output_dir)
            print(f"解压完成: {output_dir}")
            return True
        except Exception as e:
            print(f"解压失败: {e}")
            return False
    
    def _extract_zip(self, archive_path, output_dir):
        """解压ZIP文件"""
        with zipfile.ZipFile(archive_path, 'r') as zip_ref:
            zip_ref.extractall(output_dir)
    
    def _extract_rar(self, archive_path, output_dir):
        """解压RAR文件（需要unrar工具）"""
        try:
            # 尝试使用patoolib
            import patoolib
            patoolib.extract_archive(str(archive_path), outdir=str(output_dir))
        except ImportError:
            # 尝试使用命令行工具
            if shutil.which('unrar'):
                subprocess.run(['unrar', 'x', str(archive_path), str(output_dir)], check=True)
            elif shutil.which('7z'):
                subprocess.run(['7z', 'x', str(archive_path), f'-o{output_dir}'], check=True)
            else:
                raise RuntimeError("需要安装 unrar 或 7z 工具来解压RAR文件")
    
    def _extract_7z(self, archive_path, output_dir):
        """解压7Z文件"""
        try:
            import py7zr
            with py7zr.SevenZipFile(archive_path, mode='r') as z:
                z.extractall(output_dir)
        except ImportError:
            # 尝试使用7z命令行工具
            if shutil.which('7z'):
                subprocess.run(['7z', 'x', str(archive_path), f'-o{output_dir}'], check=True)
            else:
                raise RuntimeError("需要安装 py7zr 库或 7z 工具来解压7Z文件")
    
    def _extract_tar(self, archive_path, output_dir):
        """解压TAR文件"""
        with tarfile.open(archive_path, 'r') as tar:
            tar.extractall(output_dir)
    
    def _extract_tar_gz(self, archive_path, output_dir):
        """解压TAR.GZ文件"""
        with tarfile.open(archive_path, 'r:gz') as tar:
            tar.extractall(output_dir)
    
    def _extract_tar_bz2(self, archive_path, output_dir):
        """解压TAR.BZ2文件"""
        with tarfile.open(archive_path, 'r:bz2') as tar:
            tar.extractall(output_dir)
    
    def _extract_tar_xz(self, archive_path, output_dir):
        """解压TAR.XZ文件"""
        with tarfile.open(archive_path, 'r:xz') as tar:
            tar.extractall(output_dir)
    
    def _extract_gz(self, archive_path, output_dir):
        """解压GZ文件"""
        output_file = output_dir / Path(archive_path.stem)
        with gzip.open(archive_path, 'rb') as f_in:
            with open(output_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def _extract_bz2(self, archive_path, output_dir):
        """解压BZ2文件"""
        output_file = output_dir / Path(archive_path.stem)
        with bz2.open(archive_path, 'rb') as f_in:
            with open(output_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def _extract_xz(self, archive_path, output_dir):
        """解压XZ文件"""
        output_file = output_dir / Path(archive_path.stem)
        with lzma.open(archive_path, 'rb') as f_in:
            with open(output_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def list_supported_formats(self):
        """列出支持的格式"""
        return list(self.supported_formats.keys())

def main():
    parser = argparse.ArgumentParser(description='Universal Extractor - 多格式解压缩工具')
    parser.add_argument('archive', nargs='?', help='要解压的文件路径')
    parser.add_argument('-o', '--output', help='输出目录（默认为文件名同名目录）')
    parser.add_argument('--list-formats', action='store_true', help='列出支持的格式')

    args = parser.parse_args()

    extractor = UniversalExtractor()

    if args.list_formats:
        print("支持的格式:")
        for fmt in extractor.list_supported_formats():
            print(f"  {fmt}")
        return

    if not args.archive:
        parser.print_help()
        sys.exit(1)

    try:
        success = extractor.extract(args.archive, args.output)
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
