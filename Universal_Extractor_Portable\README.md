# Universal Extractor - 轻量级多格式解压缩工具

一个小巧但功能强大的解压缩工具，支持多种常见的压缩格式。

## 特点

- 🚀 **体积小巧**: 单个Python文件，无需复杂安装
- 📦 **格式丰富**: 支持ZIP、RAR、7Z、TAR、GZ、BZ2、XZ等多种格式
- 🖥️ **双界面**: 提供命令行和图形界面两种使用方式
- ⚡ **高效快速**: 基于Python标准库，性能优异
- 🔧 **易于扩展**: 模块化设计，容易添加新格式支持

## 支持的格式

### 完全支持（无需额外依赖）
- ZIP (.zip)
- TAR (.tar)
- TAR.GZ (.tar.gz, .tgz)
- TAR.BZ2 (.tar.bz2, .tbz2)
- TAR.XZ (.tar.xz, .txz)
- GZIP (.gz)
- BZIP2 (.bz2)
- XZ (.xz)

### 扩展支持（需要额外依赖或工具）
- RAR (.rar) - 需要 `unrar` 工具或 `patoolib` 库
- 7Z (.7z) - 需要 `py7zr` 库或 `7z` 工具

## 安装

### 基础安装
```bash
# 克隆或下载文件
git clone <repository-url>
cd universal-extractor

# 基础功能无需额外安装，Python 3.6+ 即可运行
python universal_extractor.py --help
```

### 完整安装（支持所有格式）
```bash
# 安装可选依赖
pip install -r requirements.txt

# 安装系统工具（可选）
# Windows: 下载并安装 7-Zip
# Linux: sudo apt-get install p7zip-full unrar
# macOS: brew install p7zip unrar
```

## 使用方法

### 命令行界面

```bash
# 基本用法
python universal_extractor.py archive.zip

# 指定输出目录
python universal_extractor.py archive.zip -o /path/to/output

# 查看支持的格式
python universal_extractor.py --list-formats

# 示例
python universal_extractor.py example.tar.gz
python universal_extractor.py data.7z -o extracted_data
python universal_extractor.py backup.rar -o backup_folder
```

### 图形界面

```bash
# 启动图形界面
python extractor_gui.py
```

图形界面功能：
- 文件浏览器选择压缩文件
- 拖拽文件到窗口
- 自定义输出目录
- 实时日志显示
- 进度条显示

## 文件结构

```
universal-extractor/
├── universal_extractor.py  # 核心解压缩模块（命令行版本）
├── extractor_gui.py       # 图形界面版本
├── requirements.txt       # 可选依赖列表
└── README.md             # 说明文档
```

## 高级用法

### 作为Python模块使用

```python
from universal_extractor import UniversalExtractor

# 创建解压器实例
extractor = UniversalExtractor()

# 解压文件
success = extractor.extract('archive.zip', 'output_folder')

# 检查支持的格式
formats = extractor.list_supported_formats()
print(formats)
```

### 批量解压

```python
import os
from universal_extractor import UniversalExtractor

extractor = UniversalExtractor()

# 批量解压目录中的所有压缩文件
for filename in os.listdir('.'):
    if any(filename.lower().endswith(ext) for ext in extractor.list_supported_formats()):
        print(f"解压 {filename}")
        extractor.extract(filename)
```

## 故障排除

### 常见问题

1. **RAR文件无法解压**
   - 安装 unrar: `sudo apt-get install unrar` (Linux)
   - 或安装 patoolib: `pip install patoolib`

2. **7Z文件无法解压**
   - 安装 py7zr: `pip install py7zr`
   - 或安装 7-Zip 工具

3. **权限错误**
   - 确保对输出目录有写权限
   - 在Windows上可能需要以管理员身份运行

### 性能优化

- 对于大文件，建议使用SSD存储
- 确保有足够的磁盘空间（至少是压缩文件大小的2倍）
- 关闭不必要的杀毒软件实时扫描

## 系统要求

- Python 3.6 或更高版本
- Windows 7+, macOS 10.12+, 或 Linux
- 至少 50MB 可用磁盘空间

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本
- 支持基本压缩格式
- 命令行和图形界面
- 跨平台支持
